<?php

namespace Modules\RajaShield\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Modules\RajaShield\Helpers\SuperAdminHelper;
use Symfony\Component\HttpFoundation\Response;

class RajaShieldMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, ?string $permission = null): Response
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            return $this->handleUnauthorized($request);
        }
        
        $user = Auth::user();
        
        // Super admin has access to everything
        if (SuperAdminHelper::isSuperAdmin($user)) {
            return $next($request);
        }
        
        // If no specific permission is provided, use route name
        if (!$permission) {
            $permission = $request->route()->getName();
        }
        
        // Check if permission is required
        if (!$permission) {
            return $next($request);
        }
        
        // Check if user has the required permission
        if (!SuperAdminHelper::hasPermission($user, $permission)) {
            return $this->handleForbidden($request, $permission);
        }
        
        return $next($request);
    }
    
    /**
     * Handle unauthorized access
     */
    protected function handleUnauthorized(Request $request): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'Unauthenticated.',
                'error' => 'authentication_required'
            ], 401);
        }
        
        return redirect()->guest(route('login'));
    }
    
    /**
     * Handle forbidden access
     */
    protected function handleForbidden(Request $request, string $permission): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'Akses ditolak. Anda tidak memiliki permission yang diperlukan.',
                'error' => 'insufficient_permissions',
                'required_permission' => $permission
            ], 403);
        }
        
        // For web requests, show 403 page or redirect
        abort(403, 'Akses ditolak. Anda tidak memiliki permission: ' . $permission);
    }
}
