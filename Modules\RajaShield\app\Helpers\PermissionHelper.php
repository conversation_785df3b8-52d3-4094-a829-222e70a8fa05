<?php

namespace Modules\RajaShield\Helpers;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\File;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use ReflectionClass;

class PermissionHelper
{
    /**
     * Cache key prefix
     */
    private static string $cachePrefix = 'rajashield';
    
    /**
     * Cache TTL in seconds
     */
    private static int $cacheTtl = 3600;
    
    /**
     * Get all route-based permissions
     */
    public static function getRoutePermissions(): array
    {
        $cacheKey = self::$cachePrefix . '.route_permissions';
        
        return Cache::remember($cacheKey, self::$cacheTtl, function () {
            $permissions = [];
            $routes = Route::getRoutes();
            
            foreach ($routes as $route) {
                $name = $route->getName();
                if ($name && !str_starts_with($name, 'generated::')) {
                    $permissions[] = [
                        'name' => $name,
                        'guard_name' => 'web',
                        'type' => 'route',
                        'panel' => self::getPanelFromRoute($name),
                    ];
                }
            }
            
            return $permissions;
        });
    }
    
    /**
     * Get all model-based permissions
     */
    public static function getModelPermissions(): array
    {
        $cacheKey = self::$cachePrefix . '.model_permissions';
        
        return Cache::remember($cacheKey, self::$cacheTtl, function () {
            $permissions = [];
            $models = self::discoverModels();
            
            foreach ($models as $model) {
                $modelName = class_basename($model);
                $actions = ['view', 'view_any', 'create', 'update', 'delete', 'delete_any'];
                
                foreach ($actions as $action) {
                    $permissions[] = [
                        'name' => $action . '_' . strtolower($modelName),
                        'guard_name' => 'web',
                        'type' => 'model',
                        'model' => $model,
                        'action' => $action,
                        'panel' => self::getPanelFromModel($model),
                    ];
                }
            }
            
            return $permissions;
        });
    }
    
    /**
     * Discover all models in the application
     */
    public static function discoverModels(): array
    {
        $cacheKey = self::$cachePrefix . '.discovered_models';
        
        return Cache::remember($cacheKey, self::$cacheTtl, function () {
            $models = [];
            $paths = config('rajashield.auto_discovery.models.paths', []);
            $exclude = config('rajashield.auto_discovery.models.exclude', []);
            
            foreach ($paths as $path) {
                $models = array_merge($models, self::scanPathForModels($path, $exclude));
            }
            
            return array_unique($models);
        });
    }
    
    /**
     * Scan path for model files
     */
    private static function scanPathForModels(string $path, array $exclude = []): array
    {
        $models = [];
        
        if (str_contains($path, '*')) {
            // Handle wildcard paths
            $basePath = str_replace('*', '', $path);
            $directories = File::glob($basePath);
            
            foreach ($directories as $directory) {
                $models = array_merge($models, self::scanDirectoryForModels($directory, $exclude));
            }
        } else {
            $models = self::scanDirectoryForModels($path, $exclude);
        }
        
        return $models;
    }
    
    /**
     * Scan directory for model files
     */
    private static function scanDirectoryForModels(string $directory, array $exclude = []): array
    {
        $models = [];
        
        if (!File::exists($directory)) {
            return $models;
        }
        
        $files = File::allFiles($directory);
        
        foreach ($files as $file) {
            if ($file->getExtension() === 'php') {
                $className = self::getClassNameFromFile($file->getPathname());
                
                if ($className && 
                    class_exists($className) && 
                    !in_array($className, $exclude) &&
                    self::isEloquentModel($className)) {
                    $models[] = $className;
                }
            }
        }
        
        return $models;
    }
    
    /**
     * Get class name from file
     */
    private static function getClassNameFromFile(string $filePath): ?string
    {
        $content = File::get($filePath);
        
        // Extract namespace
        preg_match('/namespace\s+([^;]+);/', $content, $namespaceMatches);
        $namespace = $namespaceMatches[1] ?? '';
        
        // Extract class name
        preg_match('/class\s+(\w+)/', $content, $classMatches);
        $className = $classMatches[1] ?? '';
        
        if ($namespace && $className) {
            return $namespace . '\\' . $className;
        }
        
        return null;
    }
    
    /**
     * Check if class is an Eloquent model
     */
    private static function isEloquentModel(string $className): bool
    {
        try {
            $reflection = new ReflectionClass($className);
            return $reflection->isSubclassOf('Illuminate\Database\Eloquent\Model');
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Get panel from route name
     */
    private static function getPanelFromRoute(string $routeName): string
    {
        $prefixes = config('rajashield.permission_prefixes', []);
        
        foreach ($prefixes as $panel => $prefix) {
            if (str_starts_with($routeName, $prefix . '.')) {
                return $panel;
            }
        }
        
        return 'default';
    }
    
    /**
     * Get panel from model class
     */
    private static function getPanelFromModel(string $modelClass): string
    {
        // Try to determine panel from namespace
        if (str_contains($modelClass, 'Modules\\')) {
            preg_match('/Modules\\\\(\w+)\\\\/', $modelClass, $matches);
            $moduleName = $matches[1] ?? '';
            
            $panelMap = [
                'RajaMember' => 'member',
                'RajaMarketplace' => 'admin',
                'RajaShield' => 'admin',
            ];
            
            return $panelMap[$moduleName] ?? 'admin';
        }
        
        if (str_contains($modelClass, 'App\\Aplikasi\\')) {
            preg_match('/App\\\\Aplikasi\\\\(\w+)\\\\/', $modelClass, $matches);
            $appName = strtolower($matches[1] ?? '');
            return $appName ?: 'admin';
        }
        
        return 'admin';
    }
    
    /**
     * Sync permissions to database
     */
    public static function syncPermissions(): array
    {
        $synced = ['created' => 0, 'updated' => 0, 'deleted' => 0];
        $systemType = config('rajashield.permission_system', 'dual');
        
        $allPermissions = [];
        
        if (in_array($systemType, ['route', 'dual'])) {
            $allPermissions = array_merge($allPermissions, self::getRoutePermissions());
        }
        
        if (in_array($systemType, ['model', 'dual'])) {
            $allPermissions = array_merge($allPermissions, self::getModelPermissions());
        }
        
        // Create or update permissions
        foreach ($allPermissions as $permissionData) {
            $permission = Permission::firstOrCreate(
                ['name' => $permissionData['name'], 'guard_name' => $permissionData['guard_name']],
                $permissionData
            );
            
            if ($permission->wasRecentlyCreated) {
                $synced['created']++;
            }
        }
        
        // Clear cache
        self::clearCache();
        
        return $synced;
    }
    
    /**
     * Clear all permission cache
     */
    public static function clearCache(): void
    {
        $keys = [
            self::$cachePrefix . '.route_permissions',
            self::$cachePrefix . '.model_permissions',
            self::$cachePrefix . '.discovered_models',
        ];
        
        foreach ($keys as $key) {
            Cache::forget($key);
        }
        
        // Clear spatie permission cache
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();
    }
}
