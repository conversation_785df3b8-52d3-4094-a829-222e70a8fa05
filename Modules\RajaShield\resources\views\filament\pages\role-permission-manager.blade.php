<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Form Section -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                    Role Selection & Bulk Actions
                </h3>
                {{ $this->form }}
            </div>
        </div>

        <!-- Instructions -->
        @if(!$selectedRoleId)
            <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <x-heroicon-o-information-circle class="h-5 w-5 text-blue-400" />
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">
                            Cara Menggunakan Role Permission Manager
                        </h3>
                        <div class="mt-2 text-sm text-blue-700 dark:text-blue-300">
                            <ul class="list-disc list-inside space-y-1">
                                <li>Pilih role dari dropdown di atas untuk mulai mengelola permissions</li>
                                <li>Gunakan filter untuk mencari permissions berdasarkan panel atau status</li>
                                <li>Klik tombol "Assign" atau "Revoke" untuk mengubah permission individual</li>
                                <li>Gunakan bulk actions untuk mengelola multiple permissions sekaligus</li>
                                <li>Tombol "Sync Permissions" akan memperbarui daftar permissions dari routes dan models</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        @endif

        <!-- Statistics -->
        @if($selectedRoleId)
            @php
                $role = \Spatie\Permission\Models\Role::find($selectedRoleId);
                $totalPermissions = \Spatie\Permission\Models\Permission::count();
                $assignedPermissions = $role ? $role->permissions()->count() : 0;
                $percentage = $totalPermissions > 0 ? round(($assignedPermissions / $totalPermissions) * 100, 1) : 0;
            @endphp
            
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                        Statistics for Role: {{ $role?->name }}
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                            <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                                {{ $assignedPermissions }}
                            </div>
                            <div class="text-sm text-blue-800 dark:text-blue-300">
                                Assigned Permissions
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 dark:bg-gray-900/20 p-4 rounded-lg">
                            <div class="text-2xl font-bold text-gray-600 dark:text-gray-400">
                                {{ $totalPermissions }}
                            </div>
                            <div class="text-sm text-gray-800 dark:text-gray-300">
                                Total Permissions
                            </div>
                        </div>
                        
                        <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                            <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                                {{ $percentage }}%
                            </div>
                            <div class="text-sm text-green-800 dark:text-green-300">
                                Coverage
                            </div>
                        </div>
                        
                        <div class="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
                            <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">
                                {{ $role?->users()->count() ?? 0 }}
                            </div>
                            <div class="text-sm text-purple-800 dark:text-purple-300">
                                Users with this Role
                            </div>
                        </div>
                    </div>
                    
                    <!-- Progress Bar -->
                    <div class="mt-4">
                        <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
                            <span>Permission Coverage</span>
                            <span>{{ $percentage }}%</span>
                        </div>
                        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                            <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                                 style="width: {{ $percentage }}%"></div>
                        </div>
                    </div>
                </div>
            </div>
        @endif

        <!-- Table Section -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                        Permissions Management
                    </h3>
                    
                    @if($selectedRoleId)
                        <div class="flex space-x-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                Managing: {{ $role?->name }}
                            </span>
                        </div>
                    @endif
                </div>
                
                {{ $this->table }}
            </div>
        </div>
    </div>
</x-filament-panels::page>
