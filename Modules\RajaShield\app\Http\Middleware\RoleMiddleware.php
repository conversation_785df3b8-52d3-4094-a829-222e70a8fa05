<?php

namespace Modules\RajaShield\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Modules\RajaShield\Helpers\SuperAdminHelper;
use Symfony\Component\HttpFoundation\Response;

class RoleMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string ...$roles): Response
    {
        if (!Auth::check()) {
            return $this->handleUnauthorized($request);
        }
        
        $user = Auth::user();
        
        // Super admin bypass
        if (SuperAdminHelper::isSuperAdmin($user)) {
            return $next($request);
        }
        
        // Check if user has any of the required roles
        if (!$user->hasAnyRole($roles)) {
            return $this->handleForbidden($request, $roles);
        }
        
        return $next($request);
    }
    
    /**
     * Handle unauthorized access
     */
    protected function handleUnauthorized(Request $request): Response
    {
        if ($request->expectsJson()) {
            return response()->json(['message' => 'Unauthenticated.'], 401);
        }
        
        return redirect()->guest(route('login'));
    }
    
    /**
     * Handle forbidden access
     */
    protected function handleForbidden(Request $request, array $roles): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'Akses ditolak.',
                'required_roles' => $roles
            ], 403);
        }
        
        abort(403, 'Akses ditolak. Role diperlukan: ' . implode(', ', $roles));
    }
}
