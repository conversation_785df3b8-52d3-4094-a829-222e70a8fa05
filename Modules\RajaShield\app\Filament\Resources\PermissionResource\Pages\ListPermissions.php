<?php

namespace Modules\RajaShield\Filament\Resources\PermissionResource\Pages;

use Modules\RajaShield\Filament\Resources\PermissionResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Modules\RajaShield\Helpers\PermissionHelper;
use Filament\Notifications\Notification;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;
use Spatie\Permission\Models\Permission;

class ListPermissions extends ListRecords
{
    protected static string $resource = PermissionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('sync_permissions')
                ->label('Sync Permissions')
                ->icon('heroicon-o-arrow-path')
                ->color('info')
                ->action(function () {
                    try {
                        $result = PermissionHelper::syncPermissions();
                        
                        Notification::make()
                            ->title('Permissions berhasil disinkronisasi')
                            ->body("Dibuat: {$result['created']}, Diperbarui: {$result['updated']}")
                            ->success()
                            ->send();
                            
                        // Refresh the page
                        $this->redirect(static::getUrl());
                    } catch (\Exception $e) {
                        Notification::make()
                            ->title('Gagal sinkronisasi permissions')
                            ->body($e->getMessage())
                            ->danger()
                            ->send();
                    }
                })
                ->requiresConfirmation()
                ->modalHeading('Sync Permissions')
                ->modalDescription('Apakah Anda yakin ingin melakukan sinkronisasi permissions? Ini akan menambahkan permissions baru berdasarkan routes dan models yang ditemukan.')
                ->modalSubmitActionLabel('Ya, Sync'),
                
            Actions\Action::make('clear_cache')
                ->label('Clear Cache')
                ->icon('heroicon-o-trash')
                ->color('warning')
                ->action(function () {
                    try {
                        PermissionHelper::clearCache();
                        
                        Notification::make()
                            ->title('Cache berhasil dibersihkan')
                            ->success()
                            ->send();
                    } catch (\Exception $e) {
                        Notification::make()
                            ->title('Gagal membersihkan cache')
                            ->body($e->getMessage())
                            ->danger()
                            ->send();
                    }
                }),
                
            Actions\CreateAction::make()
                ->label('Buat Permission Baru'),
        ];
    }
    
    public function getTabs(): array
    {
        $tabs = [
            'all' => Tab::make('Semua')
                ->badge(Permission::count()),
        ];
        
        // Group permissions by panel
        $panels = Permission::all()
            ->map(function ($permission) {
                $parts = explode('.', $permission->name);
                return ucfirst($parts[0] ?? 'Other');
            })
            ->unique()
            ->sort();
            
        foreach ($panels as $panel) {
            $count = Permission::where('name', 'like', strtolower($panel) . '.%')->count();
            if ($count > 0) {
                $tabs[strtolower($panel)] = Tab::make($panel)
                    ->badge($count)
                    ->modifyQueryUsing(fn (Builder $query) => 
                        $query->where('name', 'like', strtolower($panel) . '.%')
                    );
            }
        }
        
        // Add model permissions tab
        $modelCount = Permission::where('name', 'like', '%_%')
            ->where('name', 'not like', '%.%')
            ->count();
            
        if ($modelCount > 0) {
            $tabs['model'] = Tab::make('Model Permissions')
                ->badge($modelCount)
                ->modifyQueryUsing(fn (Builder $query) => 
                    $query->where('name', 'like', '%_%')
                          ->where('name', 'not like', '%.%')
                );
        }
        
        // Add unused permissions tab
        $unusedCount = Permission::doesntHave('roles')->count();
        if ($unusedCount > 0) {
            $tabs['unused'] = Tab::make('Tidak Digunakan')
                ->badge($unusedCount)
                ->badgeColor('danger')
                ->modifyQueryUsing(fn (Builder $query) => 
                    $query->doesntHave('roles')
                );
        }
        
        return $tabs;
    }
}
