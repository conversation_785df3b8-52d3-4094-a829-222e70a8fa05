<?php

namespace Modules\RajaShield\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Modules\RajaShield\Helpers\SuperAdminHelper;
use Symfony\Component\HttpFoundation\Response;

class PermissionMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $permission): Response
    {
        if (!Auth::check()) {
            return $this->handleUnauthorized($request);
        }
        
        $user = Auth::user();
        
        // Super admin bypass
        if (SuperAdminHelper::isSuperAdmin($user)) {
            return $next($request);
        }
        
        // Check permission
        if (!SuperAdminHelper::hasPermission($user, $permission)) {
            return $this->handleForbidden($request, $permission);
        }
        
        return $next($request);
    }
    
    /**
     * Handle unauthorized access
     */
    protected function handleUnauthorized(Request $request): Response
    {
        if ($request->expectsJson()) {
            return response()->json(['message' => 'Unauthenticated.'], 401);
        }
        
        return redirect()->guest(route('login'));
    }
    
    /**
     * Handle forbidden access
     */
    protected function handleForbidden(Request $request, string $permission): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'Akses ditolak.',
                'required_permission' => $permission
            ], 403);
        }
        
        abort(403, 'Akses ditolak. Permission diperlukan: ' . $permission);
    }
}
