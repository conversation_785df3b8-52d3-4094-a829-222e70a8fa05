<?php

namespace Modules\RajaShield\Filament\Resources\RoleResource\Pages;

use Modules\RajaShield\Filament\Resources\RoleResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Modules\RajaShield\Helpers\PermissionHelper;
use Filament\Notifications\Notification;

class ListRoles extends ListRecords
{
    protected static string $resource = RoleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('sync_permissions')
                ->label('Sync Permissions')
                ->icon('heroicon-o-arrow-path')
                ->color('info')
                ->action(function () {
                    try {
                        $result = PermissionHelper::syncPermissions();
                        
                        Notification::make()
                            ->title('Permissions berhasil disinkronisasi')
                            ->body("Dibuat: {$result['created']}, Diperbarui: {$result['updated']}")
                            ->success()
                            ->send();
                    } catch (\Exception $e) {
                        Notification::make()
                            ->title('Gagal sinkronisasi permissions')
                            ->body($e->getMessage())
                            ->danger()
                            ->send();
                    }
                })
                ->requiresConfirmation()
                ->modalHeading('Sync Permissions')
                ->modalDescription('Apakah Anda yakin ingin melakukan sinkronisasi permissions? Ini akan menambahkan permissions baru berdasarkan routes dan models yang ditemukan.')
                ->modalSubmitActionLabel('Ya, Sync'),
                
            Actions\CreateAction::make()
                ->label('Buat Role Baru'),
        ];
    }
    
    protected function getHeaderWidgets(): array
    {
        return [
            // Add widgets if needed
        ];
    }
}
