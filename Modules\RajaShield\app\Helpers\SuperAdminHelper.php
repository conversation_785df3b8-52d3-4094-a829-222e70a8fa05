<?php

namespace Modules\RajaShield\Helpers;

use Illuminate\Support\Facades\Cache;
use App\Models\User;
use Spatie\Permission\Models\Role;

class SuperAdminHelper
{
    /**
     * Cache key prefix
     */
    private static string $cachePrefix = 'rajashield.superadmin';
    
    /**
     * Cache TTL in seconds
     */
    private static int $cacheTtl = 3600;
    
    /**
     * Check if user is super admin
     */
    public static function isSuperAdmin(?User $user = null): bool
    {
        if (!$user) {
            $user = auth()->user();
        }
        
        if (!$user) {
            return false;
        }
        
        $cacheKey = self::$cachePrefix . '.user.' . $user->id;
        
        return Cache::remember($cacheKey, self::$cacheTtl, function () use ($user) {
            $superAdminRole = config('rajashield.super_admin_role', 'super-admin');
            return $user->hasRole($superAdminRole);
        });
    }
    
    /**
     * Check if user has permission (with super admin bypass)
     */
    public static function hasPermission(?User $user, string $permission): bool
    {
        if (!$user) {
            return false;
        }
        
        // Super admin has all permissions
        if (self::isSuperAdmin($user)) {
            return true;
        }
        
        return $user->can($permission);
    }
    
    /**
     * Check if user has any of the given permissions
     */
    public static function hasAnyPermission(?User $user, array $permissions): bool
    {
        if (!$user) {
            return false;
        }
        
        // Super admin has all permissions
        if (self::isSuperAdmin($user)) {
            return true;
        }
        
        return $user->hasAnyPermission($permissions);
    }
    
    /**
     * Check if user has all of the given permissions
     */
    public static function hasAllPermissions(?User $user, array $permissions): bool
    {
        if (!$user) {
            return false;
        }
        
        // Super admin has all permissions
        if (self::isSuperAdmin($user)) {
            return true;
        }
        
        return $user->hasAllPermissions($permissions);
    }
    
    /**
     * Get super admin role
     */
    public static function getSuperAdminRole(): ?Role
    {
        $cacheKey = self::$cachePrefix . '.role';
        
        return Cache::remember($cacheKey, self::$cacheTtl, function () {
            $superAdminRole = config('rajashield.super_admin_role', 'super-admin');
            return Role::where('name', $superAdminRole)->first();
        });
    }
    
    /**
     * Create super admin role if not exists
     */
    public static function ensureSuperAdminRole(): Role
    {
        $superAdminRole = config('rajashield.super_admin_role', 'super-admin');
        
        $role = Role::firstOrCreate([
            'name' => $superAdminRole,
            'guard_name' => 'web',
        ]);
        
        // Clear cache
        self::clearCache();
        
        return $role;
    }
    
    /**
     * Assign super admin role to user
     */
    public static function assignSuperAdminRole(User $user): bool
    {
        $role = self::ensureSuperAdminRole();
        
        if (!$user->hasRole($role)) {
            $user->assignRole($role);
            self::clearUserCache($user);
            return true;
        }
        
        return false;
    }
    
    /**
     * Remove super admin role from user
     */
    public static function removeSuperAdminRole(User $user): bool
    {
        $superAdminRole = config('rajashield.super_admin_role', 'super-admin');
        
        if ($user->hasRole($superAdminRole)) {
            $user->removeRole($superAdminRole);
            self::clearUserCache($user);
            return true;
        }
        
        return false;
    }
    
    /**
     * Get all super admin users
     */
    public static function getSuperAdminUsers(): \Illuminate\Database\Eloquent\Collection
    {
        $cacheKey = self::$cachePrefix . '.users';
        
        return Cache::remember($cacheKey, self::$cacheTtl, function () {
            $superAdminRole = config('rajashield.super_admin_role', 'super-admin');
            return User::role($superAdminRole)->get();
        });
    }
    
    /**
     * Clear user-specific cache
     */
    public static function clearUserCache(User $user): void
    {
        $cacheKey = self::$cachePrefix . '.user.' . $user->id;
        Cache::forget($cacheKey);
    }
    
    /**
     * Clear all super admin cache
     */
    public static function clearCache(): void
    {
        $keys = [
            self::$cachePrefix . '.role',
            self::$cachePrefix . '.users',
        ];
        
        foreach ($keys as $key) {
            Cache::forget($key);
        }
        
        // Clear user-specific cache for all super admins
        $users = User::role(config('rajashield.super_admin_role', 'super-admin'))->get();
        foreach ($users as $user) {
            self::clearUserCache($user);
        }
    }
    
    /**
     * Check if current user can manage roles and permissions
     */
    public static function canManageRoles(?User $user = null): bool
    {
        if (!$user) {
            $user = auth()->user();
        }
        
        if (!$user) {
            return false;
        }
        
        // Super admin can always manage roles
        if (self::isSuperAdmin($user)) {
            return true;
        }
        
        // Check specific permissions
        return $user->can('manage_roles') || $user->can('admin.roles.index');
    }
    
    /**
     * Check if current user can manage permissions
     */
    public static function canManagePermissions(?User $user = null): bool
    {
        if (!$user) {
            $user = auth()->user();
        }
        
        if (!$user) {
            return false;
        }
        
        // Super admin can always manage permissions
        if (self::isSuperAdmin($user)) {
            return true;
        }
        
        // Check specific permissions
        return $user->can('manage_permissions') || $user->can('admin.permissions.index');
    }
}
