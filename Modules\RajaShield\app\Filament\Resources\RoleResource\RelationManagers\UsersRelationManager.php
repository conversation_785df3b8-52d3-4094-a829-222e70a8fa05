<?php

namespace Modules\RajaShield\Filament\Resources\RoleResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Actions\AttachAction;
use Filament\Tables\Actions\DetachAction;
use Filament\Forms\Components\Select;
use App\Models\User;

class UsersRelationManager extends RelationManager
{
    protected static string $relationship = 'users';
    
    protected static ?string $title = 'Users';
    
    protected static ?string $label = 'User';
    
    protected static ?string $pluralLabel = 'Users';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label('Nama')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('email')
                    ->label('Email')
                    ->email()
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                TextColumn::make('name')
                    ->label('Nama')
                    ->searchable()
                    ->sortable(),
                    
                TextColumn::make('email')
                    ->label('Email')
                    ->searchable()
                    ->sortable(),
                    
                TextColumn::make('email_verified_at')
                    ->label('Email Verified')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(),
                    
                TextColumn::make('created_at')
                    ->label('Bergabung')
                    ->dateTime('d M Y')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\Filter::make('verified')
                    ->label('Email Verified')
                    ->query(fn (Builder $query): Builder => $query->whereNotNull('email_verified_at')),
            ])
            ->headerActions([
                AttachAction::make()
                    ->label('Tambah User')
                    ->form(fn (AttachAction $action): array => [
                        Select::make('recordId')
                            ->label('Pilih User')
                            ->options(function () {
                                return User::whereDoesntHave('roles', function ($query) {
                                    $query->where('id', $this->ownerRecord->id);
                                })->pluck('name', 'id');
                            })
                            ->searchable()
                            ->required(),
                    ])
                    ->preloadRecordSelect(),
            ])
            ->actions([
                DetachAction::make()
                    ->label('Hapus dari Role')
                    ->before(function ($record) {
                        // Prevent removing super-admin role from users if it's the only role
                        if ($this->ownerRecord->name === config('rajashield.super_admin_role', 'super-admin')) {
                            $userRoleCount = $record->roles()->count();
                            if ($userRoleCount <= 1) {
                                \Filament\Notifications\Notification::make()
                                    ->title('Tidak dapat menghapus role terakhir')
                                    ->body('User harus memiliki minimal satu role')
                                    ->danger()
                                    ->send();
                                return false;
                            }
                        }
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DetachBulkAction::make()
                        ->label('Hapus dari Role'),
                ]),
            ]);
    }
}
