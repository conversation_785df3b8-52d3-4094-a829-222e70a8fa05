<?php

namespace Modules\RajaShield\Filament\Pages;

use Filament\Pages\Page;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Actions\Action;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Database\Eloquent\Builder;
use Modules\RajaShield\Helpers\PermissionHelper;
use Modules\RajaShield\Helpers\SuperAdminHelper;

class RolePermissionManager extends Page implements HasForms, HasTable
{
    use InteractsWithForms, InteractsWithTable;

    protected static ?string $navigationIcon = 'heroicon-o-shield-exclamation';
    
    protected static ?string $navigationGroup = 'System';
    
    protected static ?int $navigationSort = 3;
    
    protected static string $view = 'rajashield::filament.pages.role-permission-manager';
    
    protected static ?string $navigationLabel = 'Role Permission Manager';
    
    protected static ?string $title = 'Role Permission Manager';
    
    public ?array $data = [];
    
    public ?int $selectedRoleId = null;
    
    public function mount(): void
    {
        $this->form->fill();
    }
    
    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Pilih Role')
                    ->description('Pilih role untuk mengelola permissions')
                    ->schema([
                        Select::make('selectedRoleId')
                            ->label('Role')
                            ->options(Role::all()->pluck('name', 'id'))
                            ->searchable()
                            ->placeholder('Pilih role...')
                            ->live()
                            ->afterStateUpdated(function ($state) {
                                $this->selectedRoleId = $state;
                                $this->resetTable();
                            }),
                    ])
                    ->columns(1),
                    
                Section::make('Bulk Actions')
                    ->description('Aksi untuk mengelola permissions secara massal')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                \Filament\Forms\Components\Actions::make([
                                    \Filament\Forms\Components\Actions\Action::make('assign_all_panel')
                                        ->label('Assign All Panel Permissions')
                                        ->color('success')
                                        ->action('assignAllPanelPermissions')
                                        ->requiresConfirmation()
                                        ->disabled(fn () => !$this->selectedRoleId),
                                        
                                    \Filament\Forms\Components\Actions\Action::make('revoke_all')
                                        ->label('Revoke All Permissions')
                                        ->color('danger')
                                        ->action('revokeAllPermissions')
                                        ->requiresConfirmation()
                                        ->disabled(fn () => !$this->selectedRoleId),
                                        
                                    \Filament\Forms\Components\Actions\Action::make('sync_permissions')
                                        ->label('Sync Permissions')
                                        ->color('info')
                                        ->action('syncPermissions'),
                                ]),
                            ]),
                    ])
                    ->visible(fn () => $this->selectedRoleId),
            ])
            ->statePath('data');
    }
    
    public function table(Table $table): Table
    {
        return $table
            ->query(
                Permission::query()
                    ->when($this->selectedRoleId, function (Builder $query) {
                        return $query->with(['roles' => function ($q) {
                            $q->where('id', $this->selectedRoleId);
                        }]);
                    })
            )
            ->columns([
                TextColumn::make('name')
                    ->label('Permission')
                    ->searchable()
                    ->sortable()
                    ->copyable()
                    ->weight('bold'),
                    
                TextColumn::make('panel')
                    ->label('Panel')
                    ->getStateUsing(function ($record) {
                        $parts = explode('.', $record->name);
                        return ucfirst($parts[0] ?? 'Other');
                    })
                    ->badge()
                    ->color(fn (string $state): string => match (strtolower($state)) {
                        'admin' => 'warning',
                        'kasir' => 'success',
                        'hotel' => 'info',
                        'member' => 'primary',
                        default => 'gray',
                    }),
                    
                TextColumn::make('type')
                    ->label('Tipe')
                    ->getStateUsing(function ($record) {
                        if (str_contains($record->name, '.')) {
                            return 'Route';
                        } elseif (str_contains($record->name, '_')) {
                            return 'Model';
                        }
                        return 'Custom';
                    })
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'Route' => 'primary',
                        'Model' => 'success',
                        'Custom' => 'warning',
                        default => 'gray',
                    }),
                    
                TextColumn::make('assigned')
                    ->label('Assigned')
                    ->getStateUsing(function ($record) {
                        if (!$this->selectedRoleId) {
                            return '-';
                        }
                        
                        $role = Role::find($this->selectedRoleId);
                        return $role && $role->hasPermissionTo($record) ? 'Yes' : 'No';
                    })
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'Yes' => 'success',
                        'No' => 'danger',
                        default => 'gray',
                    }),
            ])
            ->filters([
                \Filament\Tables\Filters\SelectFilter::make('panel')
                    ->label('Panel')
                    ->options(function () {
                        return Permission::all()
                            ->map(function ($permission) {
                                $parts = explode('.', $permission->name);
                                return ucfirst($parts[0] ?? 'Other');
                            })
                            ->unique()
                            ->sort()
                            ->mapWithKeys(fn ($panel) => [strtolower($panel) => $panel])
                            ->toArray();
                    })
                    ->query(function (Builder $query, array $data): Builder {
                        if (!$data['value']) {
                            return $query;
                        }
                        
                        return $query->where('name', 'like', strtolower($data['value']) . '.%');
                    }),
                    
                \Filament\Tables\Filters\SelectFilter::make('assigned')
                    ->label('Status')
                    ->options([
                        'yes' => 'Assigned',
                        'no' => 'Not Assigned',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        if (!$data['value'] || !$this->selectedRoleId) {
                            return $query;
                        }
                        
                        $role = Role::find($this->selectedRoleId);
                        if (!$role) {
                            return $query;
                        }
                        
                        if ($data['value'] === 'yes') {
                            return $query->whereHas('roles', function ($q) use ($role) {
                                $q->where('id', $role->id);
                            });
                        } else {
                            return $query->whereDoesntHave('roles', function ($q) use ($role) {
                                $q->where('id', $role->id);
                            });
                        }
                    }),
            ])
            ->actions([
                Action::make('toggle')
                    ->label(function ($record) {
                        if (!$this->selectedRoleId) {
                            return 'Select Role';
                        }
                        
                        $role = Role::find($this->selectedRoleId);
                        if ($role && $role->hasPermissionTo($record)) {
                            return 'Revoke';
                        }
                        return 'Assign';
                    })
                    ->color(function ($record) {
                        if (!$this->selectedRoleId) {
                            return 'gray';
                        }
                        
                        $role = Role::find($this->selectedRoleId);
                        if ($role && $role->hasPermissionTo($record)) {
                            return 'danger';
                        }
                        return 'success';
                    })
                    ->action(function ($record) {
                        $this->togglePermission($record);
                    })
                    ->disabled(fn () => !$this->selectedRoleId),
            ])
            ->bulkActions([
                \Filament\Tables\Actions\BulkAction::make('assign_selected')
                    ->label('Assign Selected')
                    ->color('success')
                    ->action(function ($records) {
                        $this->assignPermissions($records);
                    })
                    ->requiresConfirmation()
                    ->disabled(fn () => !$this->selectedRoleId),
                    
                \Filament\Tables\Actions\BulkAction::make('revoke_selected')
                    ->label('Revoke Selected')
                    ->color('danger')
                    ->action(function ($records) {
                        $this->revokePermissions($records);
                    })
                    ->requiresConfirmation()
                    ->disabled(fn () => !$this->selectedRoleId),
            ])
            ->defaultSort('name')
            ->striped();
    }
    
    public function togglePermission($permission): void
    {
        if (!$this->selectedRoleId) {
            return;
        }
        
        $role = Role::find($this->selectedRoleId);
        if (!$role) {
            return;
        }
        
        if ($role->hasPermissionTo($permission)) {
            $role->revokePermissionTo($permission);
            $action = 'revoked';
        } else {
            $role->givePermissionTo($permission);
            $action = 'assigned';
        }
        
        Notification::make()
            ->title("Permission {$action}")
            ->body("Permission '{$permission->name}' has been {$action} to role '{$role->name}'")
            ->success()
            ->send();
            
        $this->resetTable();
    }
    
    public function assignPermissions($permissions): void
    {
        if (!$this->selectedRoleId) {
            return;
        }
        
        $role = Role::find($this->selectedRoleId);
        if (!$role) {
            return;
        }
        
        $role->givePermissionTo($permissions);
        
        Notification::make()
            ->title('Permissions assigned')
            ->body(count($permissions) . " permissions have been assigned to role '{$role->name}'")
            ->success()
            ->send();
            
        $this->resetTable();
    }
    
    public function revokePermissions($permissions): void
    {
        if (!$this->selectedRoleId) {
            return;
        }
        
        $role = Role::find($this->selectedRoleId);
        if (!$role) {
            return;
        }
        
        $role->revokePermissionTo($permissions);
        
        Notification::make()
            ->title('Permissions revoked')
            ->body(count($permissions) . " permissions have been revoked from role '{$role->name}'")
            ->success()
            ->send();
            
        $this->resetTable();
    }
    
    public function assignAllPanelPermissions(): void
    {
        if (!$this->selectedRoleId) {
            return;
        }
        
        $role = Role::find($this->selectedRoleId);
        if (!$role) {
            return;
        }
        
        $permissions = Permission::all();
        $role->givePermissionTo($permissions);
        
        Notification::make()
            ->title('All permissions assigned')
            ->body("All permissions have been assigned to role '{$role->name}'")
            ->success()
            ->send();
            
        $this->resetTable();
    }
    
    public function revokeAllPermissions(): void
    {
        if (!$this->selectedRoleId) {
            return;
        }
        
        $role = Role::find($this->selectedRoleId);
        if (!$role) {
            return;
        }
        
        $role->revokePermissionTo($role->permissions);
        
        Notification::make()
            ->title('All permissions revoked')
            ->body("All permissions have been revoked from role '{$role->name}'")
            ->success()
            ->send();
            
        $this->resetTable();
    }
    
    public function syncPermissions(): void
    {
        try {
            $result = PermissionHelper::syncPermissions();
            
            Notification::make()
                ->title('Permissions synced')
                ->body("Created: {$result['created']}, Updated: {$result['updated']}")
                ->success()
                ->send();
                
            $this->resetTable();
        } catch (\Exception $e) {
            Notification::make()
                ->title('Sync failed')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }
    
    public static function canAccess(): bool
    {
        return SuperAdminHelper::canManageRoles() && SuperAdminHelper::canManagePermissions();
    }
}
