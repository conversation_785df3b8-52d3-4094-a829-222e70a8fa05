<?php

namespace Modules\RajaShield\Filament\Resources\PermissionResource\Pages;

use Modules\RajaShield\Filament\Resources\PermissionResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;

class EditPermission extends EditRecord
{
    protected static string $resource = PermissionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make()
                ->label('Lihat'),
            Actions\DeleteAction::make()
                ->label('Hapus')
                ->before(function () {
                    // Check if permission is used by roles
                    if ($this->record->roles()->count() > 0) {
                        Notification::make()
                            ->title('Permission masih digunakan')
                            ->body('Hapus permission dari semua roles terlebih dahulu')
                            ->danger()
                            ->send();
                        return false;
                    }
                }),
        ];
    }
    
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
    
    protected function getSavedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Permission berhasil diperbarui')
            ->body('Perubahan pada permission telah berhasil disimpan.');
    }
    
    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Auto-detect type and panel from name if not set
        if (!isset($data['type']) || empty($data['type'])) {
            if (str_contains($data['name'], '.')) {
                $data['type'] = 'route';
            } elseif (str_contains($data['name'], '_')) {
                $data['type'] = 'model';
            } else {
                $data['type'] = 'custom';
            }
        }
        
        if (!isset($data['panel']) || empty($data['panel'])) {
            if (str_contains($data['name'], '.')) {
                $parts = explode('.', $data['name']);
                $data['panel'] = $parts[0] ?? 'default';
            } else {
                $data['panel'] = 'default';
            }
        }
        
        return $data;
    }
    
    protected function afterSave(): void
    {
        // Clear permission cache after updating permission
        \Modules\RajaShield\Helpers\PermissionHelper::clearCache();
    }
}
