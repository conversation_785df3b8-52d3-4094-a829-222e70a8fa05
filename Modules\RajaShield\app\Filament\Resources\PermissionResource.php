<?php

namespace Modules\RajaShield\Filament\Resources;

use Modules\RajaShield\Filament\Resources\PermissionResource\Pages;
use Modules\RajaShield\Filament\Resources\PermissionResource\RelationManagers;
use Spatie\Permission\Models\Permission;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Modules\RajaShield\Helpers\SuperAdminHelper;

class PermissionResource extends Resource
{
    protected static ?string $model = Permission::class;

    protected static ?string $navigationIcon = 'heroicon-o-key';
    
    protected static ?string $navigationGroup = 'System';
    
    protected static ?int $navigationSort = 2;
    
    protected static ?string $navigationLabel = 'Permissions';
    
    protected static ?string $modelLabel = 'Permission';
    
    protected static ?string $pluralModelLabel = 'Permissions';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Informasi Permission')
                    ->description('Informasi dasar untuk permission')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextInput::make('name')
                                    ->label('Nama Permission')
                                    ->required()
                                    ->unique(ignoreRecord: true)
                                    ->maxLength(255)
                                    ->placeholder('Contoh: admin.users.index')
                                    ->helperText('Gunakan format: panel.resource.action atau action_model'),
                                    
                                Select::make('guard_name')
                                    ->label('Guard Name')
                                    ->options([
                                        'web' => 'Web',
                                        'api' => 'API',
                                    ])
                                    ->default('web')
                                    ->required()
                                    ->helperText('Guard yang akan digunakan untuk permission ini'),
                            ]),
                    ]),
                    
                Section::make('Metadata')
                    ->description('Informasi tambahan untuk permission')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                TextInput::make('type')
                                    ->label('Tipe')
                                    ->placeholder('route, model, custom')
                                    ->helperText('Tipe permission: route, model, atau custom'),
                                    
                                TextInput::make('panel')
                                    ->label('Panel')
                                    ->placeholder('admin, kasir, hotel')
                                    ->helperText('Panel yang menggunakan permission ini'),
                                    
                                TextInput::make('model')
                                    ->label('Model')
                                    ->placeholder('App\\Models\\User')
                                    ->helperText('Model yang terkait (untuk model permissions)'),
                            ]),
                    ])
                    ->collapsible(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label('Permission')
                    ->searchable()
                    ->sortable()
                    ->weight('bold')
                    ->copyable()
                    ->copyMessage('Permission name copied')
                    ->copyMessageDuration(1500),
                    
                TextColumn::make('guard_name')
                    ->label('Guard')
                    ->badge()
                    ->color('info'),
                    
                TextColumn::make('panel')
                    ->label('Panel')
                    ->getStateUsing(function ($record) {
                        $parts = explode('.', $record->name);
                        return ucfirst($parts[0] ?? 'Other');
                    })
                    ->badge()
                    ->color(fn (string $state): string => match (strtolower($state)) {
                        'admin' => 'warning',
                        'kasir' => 'success',
                        'hotel' => 'info',
                        'member' => 'primary',
                        default => 'gray',
                    }),
                    
                TextColumn::make('type')
                    ->label('Tipe')
                    ->getStateUsing(function ($record) {
                        if (str_contains($record->name, '.')) {
                            return 'Route';
                        } elseif (str_contains($record->name, '_')) {
                            return 'Model';
                        }
                        return 'Custom';
                    })
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'Route' => 'primary',
                        'Model' => 'success',
                        'Custom' => 'warning',
                        default => 'gray',
                    }),
                    
                TextColumn::make('roles_count')
                    ->label('Roles')
                    ->counts('roles')
                    ->badge()
                    ->color('secondary'),
                    
                TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('guard_name')
                    ->label('Guard')
                    ->options([
                        'web' => 'Web',
                        'api' => 'API',
                    ]),
                    
                SelectFilter::make('panel')
                    ->label('Panel')
                    ->options(function () {
                        return Permission::all()
                            ->map(function ($permission) {
                                $parts = explode('.', $permission->name);
                                return ucfirst($parts[0] ?? 'Other');
                            })
                            ->unique()
                            ->sort()
                            ->mapWithKeys(fn ($panel) => [strtolower($panel) => $panel])
                            ->toArray();
                    })
                    ->query(function (Builder $query, array $data): Builder {
                        if (!$data['value']) {
                            return $query;
                        }
                        
                        return $query->where('name', 'like', strtolower($data['value']) . '.%');
                    }),
                    
                SelectFilter::make('type')
                    ->label('Tipe')
                    ->options([
                        'route' => 'Route',
                        'model' => 'Model',
                        'custom' => 'Custom',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        if (!$data['value']) {
                            return $query;
                        }
                        
                        return match ($data['value']) {
                            'route' => $query->where('name', 'like', '%.%'),
                            'model' => $query->where('name', 'like', '%_%')->where('name', 'not like', '%.%'),
                            'custom' => $query->where('name', 'not like', '%.%')->where('name', 'not like', '%_%'),
                            default => $query,
                        };
                    }),
                    
                Filter::make('unused')
                    ->label('Tidak Digunakan')
                    ->query(fn (Builder $query): Builder => $query->doesntHave('roles')),
            ])
            ->actions([
                ViewAction::make()
                    ->label('Lihat'),
                EditAction::make()
                    ->label('Edit'),
                Tables\Actions\DeleteAction::make()
                    ->label('Hapus')
                    ->before(function (Permission $record) {
                        // Check if permission is used by roles
                        if ($record->roles()->count() > 0) {
                            \Filament\Notifications\Notification::make()
                                ->title('Permission masih digunakan')
                                ->body('Hapus permission dari semua roles terlebih dahulu')
                                ->danger()
                                ->send();
                            return false;
                        }
                    }),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make()
                        ->label('Hapus Terpilih')
                        ->before(function ($records) {
                            foreach ($records as $record) {
                                if ($record->roles()->count() > 0) {
                                    \Filament\Notifications\Notification::make()
                                        ->title('Beberapa permission masih digunakan')
                                        ->body('Hapus permission dari semua roles terlebih dahulu')
                                        ->danger()
                                        ->send();
                                    return false;
                                }
                            }
                        }),
                ]),
            ])
            ->defaultSort('name');
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\RolesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPermissions::route('/'),
            'create' => Pages\CreatePermission::route('/create'),
            'view' => Pages\ViewPermission::route('/{record}'),
            'edit' => Pages\EditPermission::route('/{record}/edit'),
        ];
    }
    
    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
    
    public static function canViewAny(): bool
    {
        return SuperAdminHelper::canManagePermissions();
    }
    
    public static function canCreate(): bool
    {
        return SuperAdminHelper::canManagePermissions();
    }
    
    public static function canEdit($record): bool
    {
        return SuperAdminHelper::canManagePermissions();
    }
    
    public static function canDelete($record): bool
    {
        return SuperAdminHelper::canManagePermissions();
    }
}
