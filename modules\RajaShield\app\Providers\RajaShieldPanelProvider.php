<?php

namespace Modules\RajaShield\Providers;

use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Panel;
use Filament\PanelProvider;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use Modules\RajaShield\Filament\Resources\RoleResource;
use Modules\RajaShield\Filament\Resources\PermissionResource;
use Modules\RajaShield\Filament\Pages\RolePermissionManager;
use Modules\RajaShield\Http\Middleware\RajaShieldMiddleware;

class RajaShieldPanelProvider extends PanelProvider
{
    protected string $panel = 'admin';

    public function panel(Panel $panel): Panel
    {
        return $panel
            ->id('rajashield')
            ->path('rajashield')
            ->login()
            ->brandName('RajaShield')
            ->colors([
                'primary' => \Filament\Support\Colors\Color::Blue,
                'danger' => \Filament\Support\Colors\Color::Rose,
                'success' => \Filament\Support\Colors\Color::Emerald,
                'warning' => \Filament\Support\Colors\Color::Orange,
            ])
            ->globalSearch(false)
            ->viteTheme('resources/css/filament/admin/theme.css')
            ->navigationGroups([
                \Filament\Navigation\NavigationGroup::make('System')
                    ->icon('heroicon-o-cog-6-tooth'),
            ])
            ->pages([
                RolePermissionManager::class,
            ])
            ->resources([
                RoleResource::class,
                PermissionResource::class,
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
                RajaShieldMiddleware::class,
            ]);
    }
}
