<?php

namespace Modules\RajaShield\Filament\Resources\PermissionResource\Pages;

use Modules\RajaShield\Filament\Resources\PermissionResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;

class CreatePermission extends CreateRecord
{
    protected static string $resource = PermissionResource::class;
    
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
    
    protected function getCreatedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Permission berhasil dibuat')
            ->body('Permission baru telah berhasil ditambahkan ke sistem.');
    }
    
    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Ensure guard_name is set
        if (!isset($data['guard_name'])) {
            $data['guard_name'] = 'web';
        }
        
        // Auto-detect type and panel from name
        if (!isset($data['type'])) {
            if (str_contains($data['name'], '.')) {
                $data['type'] = 'route';
            } elseif (str_contains($data['name'], '_')) {
                $data['type'] = 'model';
            } else {
                $data['type'] = 'custom';
            }
        }
        
        if (!isset($data['panel'])) {
            if (str_contains($data['name'], '.')) {
                $parts = explode('.', $data['name']);
                $data['panel'] = $parts[0] ?? 'default';
            } else {
                $data['panel'] = 'default';
            }
        }
        
        return $data;
    }
    
    protected function afterCreate(): void
    {
        // Clear permission cache after creating permission
        \Modules\RajaShield\Helpers\PermissionHelper::clearCache();
    }
}
