<?php

return [
    'name' => 'RajaShield',
    
    /*
    |--------------------------------------------------------------------------
    | Permission System Type
    |--------------------------------------------------------------------------
    |
    | Pilih tipe sistem permission yang akan digunakan:
    | 'route' - Permission berdasarkan nama route
    | 'model' - Permission berdasarkan model (view, create, update, delete)
    | 'dual' - Kombinasi keduanya
    |
    */
    'permission_system' => env('RAJASHIELD_PERMISSION_SYSTEM', 'dual'),
    
    /*
    |--------------------------------------------------------------------------
    | Super Admin Role
    |--------------------------------------------------------------------------
    |
    | Nama role untuk super admin yang memiliki akses ke semua permission
    |
    */
    'super_admin_role' => 'super-admin',
    
    /*
    |--------------------------------------------------------------------------
    | Default Member Role
    |--------------------------------------------------------------------------
    |
    | Role default untuk user baru yang mendaftar
    |
    */
    'default_member_role' => 'member',
    
    /*
    |--------------------------------------------------------------------------
    | Panel Integration
    |--------------------------------------------------------------------------
    |
    | Panel yang akan diintegrasikan dengan RajaShield
    |
    */
    'panels' => [
        'admin' => [
            'enabled' => true,
            'navigation_group' => 'System',
            'navigation_sort' => 100,
        ],
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Auto Discovery
    |--------------------------------------------------------------------------
    |
    | Konfigurasi untuk auto-discovery permissions
    |
    */
    'auto_discovery' => [
        'enabled' => true,
        'models' => [
            'enabled' => true,
            'paths' => [
                app_path('Models'),
                base_path('Modules/*/app/Models'),
            ],
            'exclude' => [
                'Spatie\Permission\Models\Permission',
                'Spatie\Permission\Models\Role',
            ],
        ],
        'resources' => [
            'enabled' => true,
            'paths' => [
                app_path('Filament/Resources'),
                base_path('Modules/*/app/Filament/Resources'),
            ],
        ],
        'widgets' => [
            'enabled' => true,
            'paths' => [
                app_path('Filament/Widgets'),
                base_path('Modules/*/app/Filament/Widgets'),
            ],
        ],
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Permission Prefixes
    |--------------------------------------------------------------------------
    |
    | Prefix untuk permission berdasarkan panel
    |
    */
    'permission_prefixes' => [
        'admin' => 'admin',
        'kasir' => 'kasir',
        'hotel' => 'hotel',
        'member' => 'member',
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Cache Settings
    |--------------------------------------------------------------------------
    |
    | Konfigurasi cache untuk optimasi performance
    |
    */
    'cache' => [
        'enabled' => true,
        'ttl' => 3600, // 1 hour
        'prefix' => 'rajashield',
        'tags' => ['permissions', 'roles'],
    ],
    
    /*
    |--------------------------------------------------------------------------
    | UI Settings
    |--------------------------------------------------------------------------
    |
    | Konfigurasi untuk tampilan UI
    |
    */
    'ui' => [
        'items_per_page' => 25,
        'show_guard_name' => false,
        'show_timestamps' => true,
    ],
];
