<?php

namespace Modules\RajaShield\Filament\Resources\RoleResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Actions\AttachAction;
use Filament\Tables\Actions\DetachAction;
use Filament\Forms\Components\Select;
use Spatie\Permission\Models\Permission;
use Filament\Tables\Filters\SelectFilter;

class PermissionsRelationManager extends RelationManager
{
    protected static string $relationship = 'permissions';
    
    protected static ?string $title = 'Permissions';
    
    protected static ?string $label = 'Permission';
    
    protected static ?string $pluralLabel = 'Permissions';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label('Nama Permission')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('guard_name')
                    ->label('Guard Name')
                    ->default('web')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                TextColumn::make('name')
                    ->label('Permission')
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color('primary'),
                    
                TextColumn::make('guard_name')
                    ->label('Guard')
                    ->badge()
                    ->color('info'),
                    
                TextColumn::make('panel')
                    ->label('Panel')
                    ->getStateUsing(function ($record) {
                        $parts = explode('.', $record->name);
                        return $parts[0] ?? 'Other';
                    })
                    ->badge()
                    ->color('secondary'),
                    
                TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d M Y')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('guard_name')
                    ->label('Guard')
                    ->options([
                        'web' => 'Web',
                        'api' => 'API',
                    ]),
                    
                SelectFilter::make('panel')
                    ->label('Panel')
                    ->options(function () {
                        return Permission::all()
                            ->map(function ($permission) {
                                $parts = explode('.', $permission->name);
                                return $parts[0] ?? 'Other';
                            })
                            ->unique()
                            ->sort()
                            ->mapWithKeys(fn ($panel) => [$panel => ucfirst($panel)])
                            ->toArray();
                    })
                    ->query(function (Builder $query, array $data): Builder {
                        if (!$data['value']) {
                            return $query;
                        }
                        
                        return $query->where('name', 'like', $data['value'] . '.%');
                    }),
            ])
            ->headerActions([
                AttachAction::make()
                    ->label('Tambah Permission')
                    ->form(fn (AttachAction $action): array => [
                        Select::make('recordId')
                            ->label('Pilih Permission')
                            ->options(function () {
                                return Permission::whereDoesntHave('roles', function ($query) {
                                    $query->where('id', $this->ownerRecord->id);
                                })
                                ->get()
                                ->groupBy(function ($permission) {
                                    $parts = explode('.', $permission->name);
                                    return $parts[0] ?? 'Other';
                                })
                                ->map(function ($permissions) {
                                    return $permissions->pluck('name', 'id');
                                })
                                ->toArray();
                            })
                            ->searchable()
                            ->required(),
                    ])
                    ->preloadRecordSelect()
                    ->multiple(),
            ])
            ->actions([
                DetachAction::make()
                    ->label('Hapus Permission'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DetachBulkAction::make()
                        ->label('Hapus Permissions'),
                ]),
            ])
            ->defaultSort('name');
    }
}
