<?php

namespace Modules\RajaShield\Filament\Resources\PermissionResource\Pages;

use Modules\RajaShield\Filament\Resources\PermissionResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\RepeatableEntry;

class ViewPermission extends ViewRecord
{
    protected static string $resource = PermissionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->label('Edit'),
        ];
    }
    
    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Informasi Permission')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('name')
                                    ->label('Nama Permission')
                                    ->badge()
                                    ->color('primary')
                                    ->copyable(),
                                    
                                TextEntry::make('guard_name')
                                    ->label('Guard Name')
                                    ->badge()
                                    ->color('info'),
                            ]),
                            
                        Grid::make(3)
                            ->schema([
                                TextEntry::make('type')
                                    ->label('Tipe')
                                    ->getStateUsing(function ($record) {
                                        if (str_contains($record->name, '.')) {
                                            return 'Route';
                                        } elseif (str_contains($record->name, '_')) {
                                            return 'Model';
                                        }
                                        return 'Custom';
                                    })
                                    ->badge()
                                    ->color(fn (string $state): string => match ($state) {
                                        'Route' => 'primary',
                                        'Model' => 'success',
                                        'Custom' => 'warning',
                                        default => 'gray',
                                    }),
                                    
                                TextEntry::make('panel')
                                    ->label('Panel')
                                    ->getStateUsing(function ($record) {
                                        $parts = explode('.', $record->name);
                                        return ucfirst($parts[0] ?? 'Other');
                                    })
                                    ->badge()
                                    ->color(fn (string $state): string => match (strtolower($state)) {
                                        'admin' => 'warning',
                                        'kasir' => 'success',
                                        'hotel' => 'info',
                                        'member' => 'primary',
                                        default => 'gray',
                                    }),
                                    
                                TextEntry::make('model')
                                    ->label('Model')
                                    ->getStateUsing(function ($record) {
                                        if (str_contains($record->name, '_')) {
                                            $parts = explode('_', $record->name);
                                            return ucfirst(end($parts));
                                        }
                                        return '-';
                                    })
                                    ->badge()
                                    ->color('secondary'),
                            ]),
                            
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('created_at')
                                    ->label('Dibuat')
                                    ->dateTime('d M Y H:i'),
                                    
                                TextEntry::make('updated_at')
                                    ->label('Diperbarui')
                                    ->dateTime('d M Y H:i'),
                            ]),
                    ]),
                    
                Section::make('Statistik')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('roles_count')
                                    ->label('Total Roles')
                                    ->state(fn ($record) => $record->roles()->count())
                                    ->badge()
                                    ->color('primary'),
                                    
                                TextEntry::make('users_count')
                                    ->label('Total Users (via Roles)')
                                    ->state(function ($record) {
                                        return $record->roles()
                                            ->withCount('users')
                                            ->get()
                                            ->sum('users_count');
                                    })
                                    ->badge()
                                    ->color('success'),
                            ]),
                    ]),
                    
                Section::make('Roles')
                    ->schema([
                        RepeatableEntry::make('roles')
                            ->label('Daftar Roles')
                            ->schema([
                                TextEntry::make('name')
                                    ->label('Role')
                                    ->badge()
                                    ->color(fn (string $state): string => match ($state) {
                                        'super-admin' => 'danger',
                                        'admin' => 'warning',
                                        'member' => 'success',
                                        default => 'gray',
                                    }),
                            ])
                            ->columns(3)
                            ->columnSpanFull(),
                    ])
                    ->collapsible()
                    ->collapsed(fn ($record) => $record->roles->count() > 5),
            ]);
    }
}
