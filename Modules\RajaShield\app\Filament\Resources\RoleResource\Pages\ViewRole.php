<?php

namespace Modules\RajaShield\Filament\Resources\RoleResource\Pages;

use Modules\RajaShield\Filament\Resources\RoleResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\RepeatableEntry;

class ViewRole extends ViewRecord
{
    protected static string $resource = RoleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->label('Edit'),
        ];
    }
    
    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Informasi Role')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('name')
                                    ->label('Nama Role')
                                    ->badge()
                                    ->color(fn (string $state): string => match ($state) {
                                        'super-admin' => 'danger',
                                        'admin' => 'warning',
                                        'member' => 'success',
                                        default => 'gray',
                                    }),
                                    
                                TextEntry::make('guard_name')
                                    ->label('Guard Name')
                                    ->badge()
                                    ->color('info'),
                            ]),
                            
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('created_at')
                                    ->label('Dibuat')
                                    ->dateTime('d M Y H:i'),
                                    
                                TextEntry::make('updated_at')
                                    ->label('Diperbarui')
                                    ->dateTime('d M Y H:i'),
                            ]),
                    ]),
                    
                Section::make('Statistik')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('permissions_count')
                                    ->label('Total Permissions')
                                    ->state(fn ($record) => $record->permissions()->count())
                                    ->badge()
                                    ->color('primary'),
                                    
                                TextEntry::make('users_count')
                                    ->label('Total Users')
                                    ->state(fn ($record) => $record->users()->count())
                                    ->badge()
                                    ->color('success'),
                            ]),
                    ]),
                    
                Section::make('Permissions')
                    ->schema([
                        RepeatableEntry::make('permissions')
                            ->label('Daftar Permissions')
                            ->schema([
                                TextEntry::make('name')
                                    ->label('Permission')
                                    ->badge()
                                    ->color('primary'),
                            ])
                            ->columns(3)
                            ->columnSpanFull(),
                    ])
                    ->collapsible()
                    ->collapsed(fn ($record) => $record->permissions->count() > 10),
                    
                Section::make('Users')
                    ->schema([
                        RepeatableEntry::make('users')
                            ->label('Daftar Users')
                            ->schema([
                                TextEntry::make('name')
                                    ->label('Nama User'),
                                TextEntry::make('email')
                                    ->label('Email'),
                            ])
                            ->columns(2)
                            ->columnSpanFull(),
                    ])
                    ->collapsible()
                    ->collapsed(fn ($record) => $record->users->count() > 5),
            ]);
    }
}
