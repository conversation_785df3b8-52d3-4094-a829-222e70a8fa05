<?php

namespace Modules\RajaShield\Filament\Resources;

use Modules\RajaShield\Filament\Resources\RoleResource\Pages;
use Modules\RajaShield\Filament\Resources\RoleResource\RelationManagers;
use Spatie\Permission\Models\Role;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\CheckboxList;
use Spatie\Permission\Models\Permission;
use Filament\Notifications\Notification;

class RoleResource extends Resource
{
    protected static ?string $model = Role::class;

    protected static ?string $navigationIcon = 'heroicon-o-shield-check';
    
    protected static ?string $navigationGroup = 'System';
    
    protected static ?int $navigationSort = 1;
    
    protected static ?string $navigationLabel = 'Roles';
    
    protected static ?string $modelLabel = 'Role';
    
    protected static ?string $pluralModelLabel = 'Roles';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Informasi Role')
                    ->description('Informasi dasar untuk role')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextInput::make('name')
                                    ->label('Nama Role')
                                    ->required()
                                    ->unique(ignoreRecord: true)
                                    ->maxLength(255)
                                    ->placeholder('Masukkan nama role')
                                    ->helperText('Nama role harus unik dan tidak boleh sama dengan role lain'),
                                    
                                Select::make('guard_name')
                                    ->label('Guard Name')
                                    ->options([
                                        'web' => 'Web',
                                        'api' => 'API',
                                    ])
                                    ->default('web')
                                    ->required()
                                    ->helperText('Guard yang akan digunakan untuk role ini'),
                            ]),
                    ]),
                    
                Section::make('Permissions')
                    ->description('Pilih permissions yang akan diberikan ke role ini')
                    ->schema([
                        CheckboxList::make('permissions')
                            ->label('Permissions')
                            ->relationship('permissions', 'name')
                            ->options(function () {
                                return Permission::all()
                                    ->groupBy(function ($permission) {
                                        // Group by panel/prefix
                                        $parts = explode('.', $permission->name);
                                        return $parts[0] ?? 'Other';
                                    })
                                    ->map(function ($permissions, $group) {
                                        return $permissions->pluck('name', 'id')->toArray();
                                    })
                                    ->toArray();
                            })
                            ->columns(3)
                            ->searchable()
                            ->bulkToggleable()
                            ->helperText('Pilih permissions yang akan diberikan ke role ini. Super admin memiliki akses ke semua permissions.'),
                    ])
                    ->collapsible()
                    ->collapsed(fn ($record) => $record && $record->permissions->count() > 10),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label('Nama Role')
                    ->searchable()
                    ->sortable()
                    ->weight('bold')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'super-admin' => 'danger',
                        'admin' => 'warning',
                        'member' => 'success',
                        default => 'gray',
                    }),
                    
                TextColumn::make('guard_name')
                    ->label('Guard')
                    ->badge()
                    ->color('info'),
                    
                TextColumn::make('permissions_count')
                    ->label('Permissions')
                    ->counts('permissions')
                    ->badge()
                    ->color('primary'),
                    
                TextColumn::make('users_count')
                    ->label('Users')
                    ->counts('users')
                    ->badge()
                    ->color('success'),
                    
                TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                    
                TextColumn::make('updated_at')
                    ->label('Diperbarui')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('guard_name')
                    ->label('Guard')
                    ->options([
                        'web' => 'Web',
                        'api' => 'API',
                    ]),
            ])
            ->actions([
                ViewAction::make()
                    ->label('Lihat'),
                EditAction::make()
                    ->label('Edit'),
                Tables\Actions\DeleteAction::make()
                    ->label('Hapus')
                    ->before(function (Role $record) {
                        // Prevent deletion of super-admin role
                        if ($record->name === config('rajashield.super_admin_role', 'super-admin')) {
                            Notification::make()
                                ->title('Tidak dapat menghapus role super admin')
                                ->danger()
                                ->send();
                            return false;
                        }
                        
                        // Check if role has users
                        if ($record->users()->count() > 0) {
                            Notification::make()
                                ->title('Role masih digunakan oleh user')
                                ->body('Hapus semua user dari role ini terlebih dahulu')
                                ->danger()
                                ->send();
                            return false;
                        }
                    }),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make()
                        ->label('Hapus Terpilih')
                        ->before(function ($records) {
                            $superAdminRole = config('rajashield.super_admin_role', 'super-admin');
                            
                            foreach ($records as $record) {
                                if ($record->name === $superAdminRole) {
                                    Notification::make()
                                        ->title('Tidak dapat menghapus role super admin')
                                        ->danger()
                                        ->send();
                                    return false;
                                }
                                
                                if ($record->users()->count() > 0) {
                                    Notification::make()
                                        ->title('Beberapa role masih digunakan oleh user')
                                        ->body('Hapus semua user dari role tersebut terlebih dahulu')
                                        ->danger()
                                        ->send();
                                    return false;
                                }
                            }
                        }),
                ]),
            ])
            ->defaultSort('name');
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\UsersRelationManager::class,
            RelationManagers\PermissionsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRoles::route('/'),
            'create' => Pages\CreateRole::route('/create'),
            'view' => Pages\ViewRole::route('/{record}'),
            'edit' => Pages\EditRole::route('/{record}/edit'),
        ];
    }
    
    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
    
    public static function canViewAny(): bool
    {
        $user = auth()->user();
        return $user && ($user->can('view_any_role') || $user->hasRole('super-admin'));
    }

    public static function canCreate(): bool
    {
        $user = auth()->user();
        return $user && ($user->can('create_role') || $user->hasRole('super-admin'));
    }

    public static function canEdit($record): bool
    {
        $user = auth()->user();
        return $user && ($user->can('update_role') || $user->hasRole('super-admin'));
    }

    public static function canDelete($record): bool
    {
        // Prevent deletion of super-admin role
        if ($record->name === config('rajashield.super_admin_role', 'super-admin')) {
            return false;
        }

        $user = auth()->user();
        return $user && ($user->can('delete_role') || $user->hasRole('super-admin'));
    }
}
