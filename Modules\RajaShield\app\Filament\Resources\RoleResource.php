<?php

namespace Modules\RajaShield\Filament\Resources;

use Modules\RajaShield\Filament\Resources\RoleResource\Pages;
use Modules\RajaShield\Filament\Resources\RoleResource\RelationManagers;
use Spatie\Permission\Models\Role;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\CheckboxList;
use Spatie\Permission\Models\Permission;
use Filament\Notifications\Notification;
use Modules\RajaShield\Helpers\SuperAdminHelper;

class RoleResource extends Resource
{
    protected static ?string $model = Role::class;

    protected static ?string $navigationIcon = 'heroicon-o-shield-check';
    
    protected static ?string $navigationGroup = 'System';
    
    protected static ?int $navigationSort = 1;
    
    protected static ?string $navigationLabel = 'Roles';
    
    protected static ?string $modelLabel = 'Role';
    
    protected static ?string $pluralModelLabel = 'Roles';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Informasi Role')
                    ->description('Informasi dasar untuk role')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextInput::make('name')
                                    ->label('Nama Role')
                                    ->required()
                                    ->unique(ignoreRecord: true)
                                    ->maxLength(255)
                                    ->placeholder('Masukkan nama role')
                                    ->helperText('Nama role harus unik dan tidak boleh sama dengan role lain'),
                                    
                                Select::make('guard_name')
                                    ->label('Guard Name')
                                    ->options([
                                        'web' => 'Web',
                                        'api' => 'API',
                                    ])
                                    ->default('web')
                                    ->required()
                                    ->helperText('Guard yang akan digunakan untuk role ini'),
                            ]),
                    ]),
                    
                Section::make('Permissions')
                    ->description('Pilih permissions yang akan diberikan ke role ini')
                    ->schema([
                        \Filament\Forms\Components\Tabs::make('PermissionTabs')
                            ->tabs([
                                \Filament\Forms\Components\Tabs\Tab::make('Admin Panel')
                                    ->schema([
                                        CheckboxList::make('admin_permissions')
                                            ->label('Admin Permissions')
                                            ->options(function () {
                                                return Permission::where('name', 'like', 'admin.%')
                                                    ->orWhere('name', 'like', 'filament.admin.%')
                                                    ->pluck('name', 'id');
                                            })
                                            ->columns(2)
                                            ->searchable()
                                            ->bulkToggleable()
                                            ->afterStateHydrated(function ($component, $state, $record) {
                                                if ($record) {
                                                    $adminPermissions = $record->permissions()
                                                        ->where(function ($query) {
                                                            $query->where('name', 'like', 'admin.%')
                                                                  ->orWhere('name', 'like', 'filament.admin.%');
                                                        })
                                                        ->pluck('id')
                                                        ->toArray();
                                                    $component->state($adminPermissions);
                                                }
                                            })
                                            ->dehydrated(false),
                                    ]),

                                \Filament\Forms\Components\Tabs\Tab::make('Kasir Panel')
                                    ->schema([
                                        CheckboxList::make('kasir_permissions')
                                            ->label('Kasir Permissions')
                                            ->options(function () {
                                                return Permission::where('name', 'like', 'kasir.%')
                                                    ->orWhere('name', 'like', 'filament.kasir.%')
                                                    ->pluck('name', 'id');
                                            })
                                            ->columns(2)
                                            ->searchable()
                                            ->bulkToggleable()
                                            ->afterStateHydrated(function ($component, $state, $record) {
                                                if ($record) {
                                                    $kasirPermissions = $record->permissions()
                                                        ->where(function ($query) {
                                                            $query->where('name', 'like', 'kasir.%')
                                                                  ->orWhere('name', 'like', 'filament.kasir.%');
                                                        })
                                                        ->pluck('id')
                                                        ->toArray();
                                                    $component->state($kasirPermissions);
                                                }
                                            })
                                            ->dehydrated(false),
                                    ]),

                                \Filament\Forms\Components\Tabs\Tab::make('Hotel Panel')
                                    ->schema([
                                        CheckboxList::make('hotel_permissions')
                                            ->label('Hotel Permissions')
                                            ->options(function () {
                                                return Permission::where('name', 'like', 'hotel.%')
                                                    ->orWhere('name', 'like', 'filament.hotel.%')
                                                    ->pluck('name', 'id');
                                            })
                                            ->columns(2)
                                            ->searchable()
                                            ->bulkToggleable()
                                            ->afterStateHydrated(function ($component, $state, $record) {
                                                if ($record) {
                                                    $hotelPermissions = $record->permissions()
                                                        ->where(function ($query) {
                                                            $query->where('name', 'like', 'hotel.%')
                                                                  ->orWhere('name', 'like', 'filament.hotel.%');
                                                        })
                                                        ->pluck('id')
                                                        ->toArray();
                                                    $component->state($hotelPermissions);
                                                }
                                            })
                                            ->dehydrated(false),
                                    ]),

                                \Filament\Forms\Components\Tabs\Tab::make('Member Panel')
                                    ->schema([
                                        CheckboxList::make('member_permissions')
                                            ->label('Member Permissions')
                                            ->options(function () {
                                                return Permission::where('name', 'like', 'member.%')
                                                    ->orWhere('name', 'like', 'filament.member.%')
                                                    ->orWhere('name', 'like', 'filament.rajamember.%')
                                                    ->pluck('name', 'id');
                                            })
                                            ->columns(2)
                                            ->searchable()
                                            ->bulkToggleable()
                                            ->afterStateHydrated(function ($component, $state, $record) {
                                                if ($record) {
                                                    $memberPermissions = $record->permissions()
                                                        ->where(function ($query) {
                                                            $query->where('name', 'like', 'member.%')
                                                                  ->orWhere('name', 'like', 'filament.member.%')
                                                                  ->orWhere('name', 'like', 'filament.rajamember.%');
                                                        })
                                                        ->pluck('id')
                                                        ->toArray();
                                                    $component->state($memberPermissions);
                                                }
                                            })
                                            ->dehydrated(false),
                                    ]),

                                \Filament\Forms\Components\Tabs\Tab::make('Model Permissions')
                                    ->schema([
                                        CheckboxList::make('model_permissions')
                                            ->label('Model Permissions')
                                            ->options(function () {
                                                return Permission::where('name', 'like', '%_%')
                                                    ->where('name', 'not like', '%.%')
                                                    ->pluck('name', 'id');
                                            })
                                            ->columns(2)
                                            ->searchable()
                                            ->bulkToggleable()
                                            ->afterStateHydrated(function ($component, $state, $record) {
                                                if ($record) {
                                                    $modelPermissions = $record->permissions()
                                                        ->where('name', 'like', '%_%')
                                                        ->where('name', 'not like', '%.%')
                                                        ->pluck('id')
                                                        ->toArray();
                                                    $component->state($modelPermissions);
                                                }
                                            })
                                            ->dehydrated(false),
                                    ]),

                                \Filament\Forms\Components\Tabs\Tab::make('Other Permissions')
                                    ->schema([
                                        CheckboxList::make('other_permissions')
                                            ->label('Other Permissions')
                                            ->options(function () {
                                                return Permission::where('name', 'not like', 'admin.%')
                                                    ->where('name', 'not like', 'kasir.%')
                                                    ->where('name', 'not like', 'hotel.%')
                                                    ->where('name', 'not like', 'member.%')
                                                    ->where('name', 'not like', 'filament.%')
                                                    ->where('name', 'not like', '%_%')
                                                    ->pluck('name', 'id');
                                            })
                                            ->columns(2)
                                            ->searchable()
                                            ->bulkToggleable()
                                            ->afterStateHydrated(function ($component, $state, $record) {
                                                if ($record) {
                                                    $otherPermissions = $record->permissions()
                                                        ->where('name', 'not like', 'admin.%')
                                                        ->where('name', 'not like', 'kasir.%')
                                                        ->where('name', 'not like', 'hotel.%')
                                                        ->where('name', 'not like', 'member.%')
                                                        ->where('name', 'not like', 'filament.%')
                                                        ->where('name', 'not like', '%_%')
                                                        ->pluck('id')
                                                        ->toArray();
                                                    $component->state($otherPermissions);
                                                }
                                            })
                                            ->dehydrated(false),
                                    ]),

                                \Filament\Forms\Components\Tabs\Tab::make('All Permissions')
                                    ->schema([
                                        CheckboxList::make('permissions')
                                            ->label('All Permissions')
                                            ->relationship('permissions', 'name')
                                            ->options(Permission::all()->pluck('name', 'id'))
                                            ->columns(2)
                                            ->searchable()
                                            ->bulkToggleable()
                                            ->helperText('Semua permissions dalam satu tampilan untuk kemudahan pencarian.'),
                                    ]),
                            ])
                            ->columnSpanFull(),
                    ])
                    ->collapsible()
                    ->collapsed(fn ($record) => $record && $record->permissions->count() > 10),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label('Nama Role')
                    ->searchable()
                    ->sortable()
                    ->weight('bold')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'super-admin' => 'danger',
                        'admin' => 'warning',
                        'member' => 'success',
                        default => 'gray',
                    }),
                    
                TextColumn::make('guard_name')
                    ->label('Guard')
                    ->badge()
                    ->color('info'),
                    
                TextColumn::make('permissions_count')
                    ->label('Permissions')
                    ->counts('permissions')
                    ->badge()
                    ->color('primary'),
                    
                TextColumn::make('users_count')
                    ->label('Users')
                    ->counts('users')
                    ->badge()
                    ->color('success'),
                    
                TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                    
                TextColumn::make('updated_at')
                    ->label('Diperbarui')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                \Filament\Tables\Filters\SelectFilter::make('guard_name')
                    ->label('Guard')
                    ->options([
                        'web' => 'Web',
                        'api' => 'API',
                    ]),
            ])
            ->actions([
                ViewAction::make()
                    ->label('Lihat'),
                EditAction::make()
                    ->label('Edit'),
                \Filament\Tables\Actions\DeleteAction::make()
                    ->label('Hapus')
                    ->before(function (Role $record) {
                        // Prevent deletion of super-admin role
                        if ($record->name === config('rajashield.super_admin_role', 'super-admin')) {
                            Notification::make()
                                ->title('Tidak dapat menghapus role super admin')
                                ->danger()
                                ->send();
                            return false;
                        }
                        
                        // Check if role has users
                        if ($record->users()->count() > 0) {
                            Notification::make()
                                ->title('Role masih digunakan oleh user')
                                ->body('Hapus semua user dari role ini terlebih dahulu')
                                ->danger()
                                ->send();
                            return false;
                        }
                    }),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make()
                        ->label('Hapus Terpilih')
                        ->before(function ($records) {
                            $superAdminRole = config('rajashield.super_admin_role', 'super-admin');
                            
                            foreach ($records as $record) {
                                if ($record->name === $superAdminRole) {
                                    Notification::make()
                                        ->title('Tidak dapat menghapus role super admin')
                                        ->danger()
                                        ->send();
                                    return false;
                                }
                                
                                if ($record->users()->count() > 0) {
                                    Notification::make()
                                        ->title('Beberapa role masih digunakan oleh user')
                                        ->body('Hapus semua user dari role tersebut terlebih dahulu')
                                        ->danger()
                                        ->send();
                                    return false;
                                }
                            }
                        }),
                ]),
            ])
            ->defaultSort('name');
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\UsersRelationManager::class,
            RelationManagers\PermissionsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRoles::route('/'),
            'create' => Pages\CreateRole::route('/create'),
            'view' => Pages\ViewRole::route('/{record}'),
            'edit' => Pages\EditRole::route('/{record}/edit'),
        ];
    }
    
    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
    
    public static function canViewAny(): bool
    {
        return SuperAdminHelper::canManageRoles();
    }
    
    public static function canCreate(): bool
    {
        return SuperAdminHelper::canManageRoles();
    }
    
    public static function canEdit($record): bool
    {
        return SuperAdminHelper::canManageRoles();
    }
    
    public static function canDelete($record): bool
    {
        // Prevent deletion of super-admin role
        if ($record->name === config('rajashield.super_admin_role', 'super-admin')) {
            return false;
        }
        
        return SuperAdminHelper::canManageRoles();
    }
}
