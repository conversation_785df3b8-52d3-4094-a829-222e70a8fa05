<?php

namespace Modules\RajaShield\Filament\Resources\RoleResource\Pages;

use Mo<PERSON>les\RajaShield\Filament\Resources\RoleResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;

class EditRole extends EditRecord
{
    protected static string $resource = RoleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make()
                ->label('Lihat'),
            Actions\DeleteAction::make()
                ->label('Hapus')
                ->before(function () {
                    // Prevent deletion of super-admin role
                    if ($this->record->name === config('rajashield.super_admin_role', 'super-admin')) {
                        Notification::make()
                            ->title('Tidak dapat menghapus role super admin')
                            ->danger()
                            ->send();
                        return false;
                    }
                    
                    // Check if role has users
                    if ($this->record->users()->count() > 0) {
                        Notification::make()
                            ->title('Role masih digunakan oleh user')
                            ->body('Hapus semua user dari role ini terlebih dahulu')
                            ->danger()
                            ->send();
                        return false;
                    }
                }),
        ];
    }
    
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
    
    protected function getSavedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Role berhasil diperbarui')
            ->body('Perubahan pada role telah berhasil disimpan.');
    }
    
    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Prevent changing super-admin role name
        if ($this->record->name === config('rajashield.super_admin_role', 'super-admin')) {
            $data['name'] = $this->record->name;
        }
        
        return $data;
    }
    
    protected function afterSave(): void
    {
        // Clear permission cache after updating role
        \Modules\RajaShield\Helpers\PermissionHelper::clearCache();
        \Modules\RajaShield\Helpers\SuperAdminHelper::clearCache();
    }
}
