<?php

namespace Modules\RajaShield\Traits;

use Mo<PERSON>les\RajaShield\Helpers\SuperAdminHelper;
use Illuminate\Support\Facades\Cache;

trait HasRajaShieldPermissions
{
    /**
     * Cache TTL for permission checks
     */
    protected int $permissionCacheTtl = 300; // 5 minutes
    
    /**
     * Check if user can view any records of this model
     */
    public function canViewAny(): bool
    {
        $permission = 'view_any_' . strtolower(class_basename(static::class));
        return SuperAdminHelper::hasPermission(auth()->user(), $permission);
    }
    
    /**
     * Check if user can view this specific record
     */
    public function canView(): bool
    {
        $permission = 'view_' . strtolower(class_basename(static::class));
        return SuperAdminHelper::hasPermission(auth()->user(), $permission);
    }
    
    /**
     * Check if user can create records of this model
     */
    public function canCreate(): bool
    {
        $permission = 'create_' . strtolower(class_basename(static::class));
        return SuperAdminHelper::hasPermission(auth()->user(), $permission);
    }
    
    /**
     * Check if user can update this specific record
     */
    public function canUpdate(): bool
    {
        $permission = 'update_' . strtolower(class_basename(static::class));
        return SuperAdminHelper::hasPermission(auth()->user(), $permission);
    }
    
    /**
     * Check if user can delete this specific record
     */
    public function canDelete(): bool
    {
        $permission = 'delete_' . strtolower(class_basename(static::class));
        return SuperAdminHelper::hasPermission(auth()->user(), $permission);
    }
    
    /**
     * Check if user can delete any records of this model
     */
    public function canDeleteAny(): bool
    {
        $permission = 'delete_any_' . strtolower(class_basename(static::class));
        return SuperAdminHelper::hasPermission(auth()->user(), $permission);
    }
    
    /**
     * Check if user can restore this specific record
     */
    public function canRestore(): bool
    {
        $permission = 'restore_' . strtolower(class_basename(static::class));
        return SuperAdminHelper::hasPermission(auth()->user(), $permission);
    }
    
    /**
     * Check if user can restore any records of this model
     */
    public function canRestoreAny(): bool
    {
        $permission = 'restore_any_' . strtolower(class_basename(static::class));
        return SuperAdminHelper::hasPermission(auth()->user(), $permission);
    }
    
    /**
     * Check if user can force delete this specific record
     */
    public function canForceDelete(): bool
    {
        $permission = 'force_delete_' . strtolower(class_basename(static::class));
        return SuperAdminHelper::hasPermission(auth()->user(), $permission);
    }
    
    /**
     * Check if user can force delete any records of this model
     */
    public function canForceDeleteAny(): bool
    {
        $permission = 'force_delete_any_' . strtolower(class_basename(static::class));
        return SuperAdminHelper::hasPermission(auth()->user(), $permission);
    }
    
    /**
     * Get all permissions for this model
     */
    public function getModelPermissions(): array
    {
        $modelName = strtolower(class_basename(static::class));
        
        return [
            'view_any_' . $modelName,
            'view_' . $modelName,
            'create_' . $modelName,
            'update_' . $modelName,
            'delete_' . $modelName,
            'delete_any_' . $modelName,
            'restore_' . $modelName,
            'restore_any_' . $modelName,
            'force_delete_' . $modelName,
            'force_delete_any_' . $modelName,
        ];
    }
    
    /**
     * Check if user has any permission for this model
     */
    public function hasAnyModelPermission(): bool
    {
        $permissions = $this->getModelPermissions();
        return SuperAdminHelper::hasAnyPermission(auth()->user(), $permissions);
    }
    
    /**
     * Check if user has all permissions for this model
     */
    public function hasAllModelPermissions(): bool
    {
        $permissions = $this->getModelPermissions();
        return SuperAdminHelper::hasAllPermissions(auth()->user(), $permissions);
    }
    
    /**
     * Scope query to only include records user can view
     */
    public function scopeCanView($query)
    {
        $user = auth()->user();
        
        if (!$user) {
            return $query->whereRaw('1 = 0'); // No access for guests
        }
        
        // Super admin can view all
        if (SuperAdminHelper::isSuperAdmin($user)) {
            return $query;
        }
        
        // Check if user has view_any permission
        if ($this->canViewAny()) {
            return $query;
        }
        
        // If model has user_id field, only show user's own records
        if (in_array('user_id', $this->getFillable()) || $this->hasAttribute('user_id')) {
            return $query->where('user_id', $user->id);
        }
        
        // Default: no access
        return $query->whereRaw('1 = 0');
    }
    
    /**
     * Check if user can perform specific action on this model
     */
    public function canPerformAction(string $action): bool
    {
        $permission = $action . '_' . strtolower(class_basename(static::class));
        return SuperAdminHelper::hasPermission(auth()->user(), $permission);
    }
    
    /**
     * Get cached permission result
     */
    protected function getCachedPermission(string $permission): bool
    {
        $user = auth()->user();
        if (!$user) {
            return false;
        }
        
        $cacheKey = "rajashield.user.{$user->id}.permission.{$permission}";
        
        return Cache::remember($cacheKey, $this->permissionCacheTtl, function () use ($user, $permission) {
            return SuperAdminHelper::hasPermission($user, $permission);
        });
    }
    
    /**
     * Clear permission cache for current user
     */
    public function clearPermissionCache(): void
    {
        $user = auth()->user();
        if (!$user) {
            return;
        }
        
        $permissions = $this->getModelPermissions();
        foreach ($permissions as $permission) {
            $cacheKey = "rajashield.user.{$user->id}.permission.{$permission}";
            Cache::forget($cacheKey);
        }
    }
}
